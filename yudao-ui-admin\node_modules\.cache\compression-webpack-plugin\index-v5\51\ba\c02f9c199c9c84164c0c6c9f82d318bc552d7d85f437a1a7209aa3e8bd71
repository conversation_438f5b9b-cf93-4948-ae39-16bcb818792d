
56bf3325dbadce09532dddf2af374fa89a0e74b2	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"7c0e076a201b2fb96fe652d509a6b6d3\"}","integrity":"sha512-iHLostegp7IgLPH9rsRo78dV7AaotBZjPNSERWjsmEK79imtQajuTGw1d9OapnB4yN/sl7D2bkCoUm2LeJBCyQ==","time":1754358297458,"size":16761184}