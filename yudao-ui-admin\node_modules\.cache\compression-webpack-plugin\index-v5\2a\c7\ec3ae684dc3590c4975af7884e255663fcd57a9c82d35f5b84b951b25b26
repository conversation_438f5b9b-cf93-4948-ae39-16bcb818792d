
1e89223f450e9e9e27736eeae22b074fad757b2d	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"23b60a43cf018d9da28799a47aef9c89\"}","integrity":"sha512-EzgLqo09Nf+iPeQrjyJceZnC5/0PxlFebXD4XXY54uZkMp4O1W4vhCDZvMu5My4aN0w/MpRpmYs2I57mWBfRdw==","time":1754358310001,"size":16762506}