
9e15b0d24c2bb9f38693ee6779984fd7a655d60b	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"cac1fe1844a86bb82c2e2a56aca3e19b\"}","integrity":"sha512-j/ae0bU87qrfzW7b+Ikrc7B07531IZGrYciQwrfrc6Yi6iDslmvmhSJf6waXcNqWJqEeB3U01LcQ9Y5hckRNVw==","time":1754358526536,"size":3474350}