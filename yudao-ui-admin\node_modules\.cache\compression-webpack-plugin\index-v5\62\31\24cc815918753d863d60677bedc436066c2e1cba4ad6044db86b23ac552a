
dfa01ebdf264e9883f88ac6adba1a73ce3d3c257	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"e3a950a3773cfd7df5c944a677db22fc\"}","integrity":"sha512-GuowKJBtWkEtB5n+OvA+Oc9XqR74z++jPBY21kbexKOyzfn9Ro2SKazCxbfibjZz7k4pQS4quAPQsY+oYqalhA==","time":1754360472841,"size":3473648}