
19e882ee6d9d1b44d543481c53e07471460381c3	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"9779828189879f86dab26b68a7ad55c7\"}","integrity":"sha512-5C83P8e6TSMnpnJTJk5NFCjglycY28Qwcx49ekO2a7q5Y6fJVW1lFJA67t1wLvHTF0egfKhG3GwMEEZcAGH++g==","time":1754359322982,"size":3473645}