{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\area\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\area\\index.vue", "mtime": 1754358495639}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/insurance/area", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"区域名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入区域名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"保险公司\" prop=\"companyId\">\r\n        <CompanySelect v-model=\"queryParams.companyId\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:area:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:area:export']\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"区域名称\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleViewCompany(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:query']\">查看负责公司</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleSetCompany(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:update']\">关联负责公司</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:update']\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:delete']\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 对话框(添加 / 修改) -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"区域名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入区域名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"子区域\" prop=\"subArea\">\r\n          <el-select\r\n            v-model=\"form.subArea\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请选择子区域\">\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"负责公司列表\" :visible.sync=\"areaCompany.listOpen\" width=\"500px\" append-to-body>\r\n      <AreaCompanyList v-if=\"areaCompany.listOpen\" :areaId=\"areaCompany.areaId\" />\r\n    </el-dialog>\r\n    <el-dialog :title=\"areaCompany.chooseTitle\" :visible.sync=\"areaCompany.createOpen\" width=\"500px\" append-to-body>\r\n      <AreaCompanyChoose ref=\"companyChooser\" :areaId=\"areaCompany.areaId\" @submitSuccess=\"handleAreaCompanySubmitSuccess\"/>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAreaCompanyForm\">确 定</el-button>\r\n        <el-button @click=\"cancelAreaCompanyForm\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { createArea, updateArea, deleteArea, getArea, getAreaPage, exportAreaExcel } from \"@/api/insurance/area\";\r\nimport CompanySelect from \"@/views/insurance/company/components/companySelect\";\r\nimport AreaCompanyList from \"./areaCompanyList\";\r\nimport AreaCompanyChoose from \"./areaCompanyChoose\";\r\nexport default {\r\n  name: \"Area\",\r\n  components: {\r\n    CompanySelect,\r\n    AreaCompanyList,\r\n    AreaCompanyChoose\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 区域列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        name: null,\r\n        companyId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"区域名称不能为空\", trigger: \"blur\" }],\r\n      },\r\n      areaCompany: {\r\n        listOpen: false,\r\n        createOpen: false,\r\n        areaId: undefined,\r\n        chooseTitle: undefined,\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      // 执行查询\r\n      getAreaPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        companyId: undefined,\r\n        subArea: [],\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加区域\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getArea(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改区域\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateArea(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createArea(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除区域编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteArea(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有区域数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportAreaExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '区域.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleViewCompany(row) {\r\n      this.areaCompany.listOpen = true;\r\n      this.areaCompany.areaId = row.id;\r\n    },\r\n    handleSetCompany(row) {\r\n      this.areaCompany.createOpen = true;\r\n      this.areaCompany.areaId = row.id;\r\n      this.areaCompany.chooseTitle = `关联负责公司:${row.name}`;\r\n      this.$nextTick(()=>this.$refs.companyChooser.reset()) \r\n    },\r\n    submitAreaCompanyForm() {\r\n      this.$refs.companyChooser.submit();\r\n    },\r\n    cancelAreaCompanyForm() {\r\n      this.areaCompany.createOpen = false;\r\n      this.$refs.companyChooser.reset()\r\n    },\r\n    handleAreaCompanySubmitSuccess() {\r\n      this.$modal.msgSuccess(\"关联负责公司成功\");\r\n      this.areaCompany.createOpen = false;\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}