
dde9c3bd111bf89271aefa3532d6fd77d14888f3	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"281b696509d0d554ada876fc94e033ed\"}","integrity":"sha512-XTBxYL0JIxQxP93FVIHItmedi/R1z7wivQEAtnz2jcNLlUNPMj6Q1UecvzxNDb3jkjIMbZ9D8WwQkv+x4P7kdg==","time":1754360975553,"size":16763296}