
613334bc6b5118f0a2ba34ebef6b93c65c1f603b	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"7d822d2ae249d24f498b5e6302984d00\"}","integrity":"sha512-pKZP2Uw6RIV8b1x9xRT7FToy0WkzwJssVVHkT+k20ohbHpobSmmEBvbtyr0yz1rgpJfwYIfnKh2Zc4LuTWUd/g==","time":1754358343028,"size":16762323}