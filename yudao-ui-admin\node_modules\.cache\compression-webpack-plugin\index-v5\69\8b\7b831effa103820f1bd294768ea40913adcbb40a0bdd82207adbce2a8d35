
0b475cb47eb92ac1de1975db5f1b00833bf974fb	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"d560af4de37759b9ea6390e8f599b9f6\"}","integrity":"sha512-UvHUDo6SM0OLanWFtHI6rCAdoLMWftihGv2uAQ+XgzAwVHxnihh0PA66WBJNpSD1hz2xI3ME1sDGWHB4BQf6ig==","time":1754360456480,"size":3475476}