
1259fdef59b05630938c1f23904e715166196dab	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.eeeb03066b870576afb2.hot-update.js\",\"contentHash\":\"1288f00b37f5ad290d6158fd7e727111\"}","integrity":"sha512-p8aEYZS8nwXApKNMKPiGAk4fKmMPAsKOneCu+9YD4um9e5Dm49WGO/qM5jtxC/wnd1HlX/4lyM4QewOHbLo/EQ==","time":1754359294416,"size":85822}