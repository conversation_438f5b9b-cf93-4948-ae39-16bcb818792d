
dc005a4a51d6ba73d9948a42172d29bcdcd1d416	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"add489c182b113e0fd14fa28546122ea\"}","integrity":"sha512-rDRGPLjuyV9rG451+FyuIeMkAwvslEJQnhZKXDQqV24gm+oD9d4RrnsntfTyTcKVKQDkXj8E+neQd8uy/N66Ng==","time":1754359294752,"size":3474877}