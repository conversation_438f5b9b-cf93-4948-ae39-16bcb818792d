
1866338bad347b2cf00fa3f8184744422eae610d	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"f13b5d11c83d58d741fbce67cfb68b26\"}","integrity":"sha512-zuaow8U8PdxaJ3wyKbX9zmxoKwweI4+PwZSlulnG/uQ46+HkdMn6AtPX5budMpDvPu65yc5Cj4xSVY7VEWrZGw==","time":1754358518364,"size":3474353}