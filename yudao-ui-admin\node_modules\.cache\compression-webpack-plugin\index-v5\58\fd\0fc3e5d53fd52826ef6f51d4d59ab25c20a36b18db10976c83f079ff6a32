
204d6d0369e9d3c66dc4f9f58d47e69e8c637ee5	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.9a978f376d17d4d4e6c8.hot-update.js\",\"contentHash\":\"ae7256637f987e9e348d1bd202a5b588\"}","integrity":"sha512-qTIYk3bnm12NswliZ84nv7u/p3uRRbvRq5JpGTGAw0PSGjBCGTw//q2wscaHDp8K5exZR954bKSSl3+dXwRk/A==","time":1754358295839,"size":85600}