
daa7757e9b6a3ecf398e1f008d42f7e968f74894	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"3b3aa716a5f33567f69fcd572e3bb15f\"}","integrity":"sha512-PTHNKPsaspHny7qxGqvGLkQ+gYl+MUF3JVqqFTloLkvhBCg+gLzYbU3SbKorZuLM9Mckig6Izmrq+oZbqExCrA==","time":1754358527441,"size":16761302}