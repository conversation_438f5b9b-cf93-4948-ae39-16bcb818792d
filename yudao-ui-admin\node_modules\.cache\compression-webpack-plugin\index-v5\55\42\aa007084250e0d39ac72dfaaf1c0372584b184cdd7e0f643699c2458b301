
fe7f689a02680a03dd0ac89acb3a3f578b142d5b	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.1c3d9e9d083f184d8e5d.hot-update.js\",\"contentHash\":\"0fc454c68b019ce2efb744403f83d24c\"}","integrity":"sha512-eVUP6yNnFzygIzm3M59142GrUieFhgNmdmy6WQI779WhRJDS+c888wkseOACc1CsToz9YqN8VqNqA7KFN0ff5w==","time":1754360443051,"size":177838}