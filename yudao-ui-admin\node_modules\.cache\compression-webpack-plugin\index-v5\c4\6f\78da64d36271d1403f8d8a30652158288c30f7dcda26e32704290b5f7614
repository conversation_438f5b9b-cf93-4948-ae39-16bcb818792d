
f8981d5f53db522b52eb1cd4fe0056017c4fb9f5	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.90fae767706c7866dd0c.hot-update.js\",\"contentHash\":\"6311c6bbe17cb0ce2659426d069b0653\"}","integrity":"sha512-MalfqMSCMo3dmiyXFG7BJ8EA7iczlilcneOVyVTmwgBRixxUoE+AEMncH8huVUuS0TJTO5Ouy0pjrJpZ4OCebQ==","time":1754358341422,"size":85773}