
5e0ac596b54246702a0c12fdee491bbea336110b	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"b6a7bdbb8b322862a6176a54ed2d865a\"}","integrity":"sha512-k9oN6y0EhbpCRR3/GRsKdbIMVlvpQhqmVKcrucXYl/FsG98JIUrU5xDmJLraQBqC4I1Vl506Z9AIu4nAXLaWEA==","time":1754358341800,"size":3474275}