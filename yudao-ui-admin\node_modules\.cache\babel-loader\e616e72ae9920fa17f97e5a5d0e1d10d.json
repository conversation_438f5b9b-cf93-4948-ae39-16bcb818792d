{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\workOrder\\index.vue", "mtime": 1754360453449}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqbA;;AAoBA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;eACA;EACAA,iBADA;EAEAC;IACAC,iCADA;IAEAC,gCAFA;IAGAC,qBAHA;IAIAC,0BAJA;IAKAC,qBALA;IAMAC,6BANA;IAOAC,2BAPA;IAQAC,+BARA;IASAC,qCATA;IAUAC;EAVA,CAFA;EAcAC,IAdA,kBAcA;IAEA;MACA;MACAC,aAFA;MAGA;MACAC,oBAJA;MAKA;MACAC,gBANA;MAOA;MACAC,QARA;MASA;MACAC,QAVA;MAWA;MACAC,SAZA;MAaA;MACAC,WAdA;MAeAC,6BAfA;MAgBAC,uBAhBA;MAiBAC,8BAjBA;MAkBA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAC,UAHA;QAIA1B,UAJA;QAKA2B,kBALA;QAMAC,uBANA;QAOAC,+DAPA;QAQAC,oDARA;QASAC,kBATA;QAUAC,eAVA;QAWAC;MAXA,CAnBA;MAgCA;MACAC,QAjCA;MAkCA;MACAC;QACAnC;UAAAoC;UAAAC;UAAAC;QAAA,EADA;QAEAX;UAAAS;UAAAC;UAAAC;QAAA;MAFA,CAnCA;MAuCAC,mBAvCA;MAwCAC,sBAxCA;MAyCAC,iBAzCA;MA0CAC,kBA1CA;MA2CAC,iBA3CA;MA4CAC,gBA5CA;MA6CAC,gBA7CA;MA8CAC,gBA9CA;MA+CApC;QACAS,WADA;QAEA4B;MAFA,CA/CA;MAmDA3C;QACA2C,gBADA;QAEA/C;MAFA,CAnDA;MAuDAgD,4BAvDA;MAwDAC,wBAxDA;MAyDAC,oBAzDA;MA0DA3C;QACAwC;MADA,CA1DA;MA6DAI,iBA7DA;MA8DAJ,gBA9DA;MA+DAK;QACAC,sBADA;QAEAN;MAFA,CA/DA;MAmEAtC;QACAU,WADA;QAEA4B;MAFA,CAnEA;MAuEAO,iBAvEA;MAwEA;MACAC;QACA;QACApC,WAFA;QAGA;QACAD,SAJA;QAKA;QACAsC,kBANA;QAOA;QACAC,sCARA;QASA;QACAC,kGAVA;QAWAC,+GAXA;QAYAC;MAZA,CAzEA;MAuFAC;QACA1C,WADA;QAEA2C,aAFA;QAGAC;MAHA,CAvFA;MA4FAC;QACAC,iCADA;QAEAC,6BAFA;QAGAC,iBAHA;QAIAC,2BAJA;QAKAC,sCALA;QAMAC,4BANA;QAOAC,gCAPA;QAQAC;MARA,CA5FA;MAsGAC,yBAtGA;MAuGAC,cAvGA;MAwGAC,cAxGA;MAyGAb,aAzGA;MA0GA;MACAc;QACAC,cADA;QAEAhE,cAFA;QAGAG,QAHA;QAIAC,QAJA;QAKA6D,aALA;QAMAvD;UACAC,SADA;UAEAC,YAFA;UAGAsD,mBAHA;UAIAC,YAJA;UAKAC,kBALA;UAMAC,oBANA;UAOAC;QAPA;MANA,CA3GA;MA2HA;MACAC;QACAP,cADA;QAEAhE,cAFA;QAGAqB;UACAmD;QADA;MAHA;IA5HA;EAoIA,CApJA;EAqJAC,OArJA,qBAqJA;IAAA;;IACA;IACA;MACA;IACA,CAFA;EAGA,CA1JA;EA2JAC;IACAC,gBADA,8BACA;MACA;MACA;IACA,CAJA;IAKAC,aALA,yBAKAC,OALA,EAKAC,KALA,EAKAC,GALA,EAKA;MACA;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;UACA;;QACA;UACA;MAXA;IAaA,CAnBA;;IAoBA;IACAC,OArBA,qBAqBA;MAAA;;MACA,oBADA,CAEA;;MACA;MACA;MACA;MACA,wEANA,CAOA;;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAlCA;IAmCAC,SAnCA,uBAmCA;MACA;IACA,CArCA;;IAsCA;IACAC,MAvCA,oBAuCA;MACA;MACA;MACA;MACA;MACA;IACA,CA7CA;;IA8CA;IACAC,KA/CA,mBA+CA;MACA;QACAC,aADA;QAEAjG,eAFA;QAGA2B,uBAHA;QAIAC,4BAJA;QAKAsE,kBALA;QAMAnE,uBANA;QAOAoE,kBAPA;QAQAC,eARA;QASAC,wBATA;QAUAC,kBAVA;QAWAC,mBAXA;QAYAC,2BAZA;QAaAC,qBAbA;QAcAC,sBAdA;QAeAC,mCAfA;QAgBAC,sBAhBA;QAiBAC,2BAjBA;QAkBAC;MAlBA;MAoBA;IACA,CArEA;;IAsEA;IACAC,WAvEA,yBAuEA;MACA;MACA;IACA,CA1EA;;IA2EA;IACAC,UA5EA,wBA4EA;MACA;MACA;MACA;MACA;IACA,CAjFA;;IAkFA;IACAC,SAnFA,uBAmFA;MACA;MACA;MACA;IACA,CAvFA;;IAwFA;IACAC,YAzFA,wBAyFAtB,GAzFA,EAyFA;MAAA;;MACA;MACA;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAjGA;IAkGAuB,eAlGA,2BAkGAvB,GAlGA,EAkGA;MAAA;;MACA;QACA;MACA,CAFA;IAGA,CAtGA;IAuGAwB,YAvGA,wBAuGAxB,GAvGA,EAuGA;MACA;MACA;MACA;IACA,CA3GA;IA4GAyB,aA5GA,yBA4GAzB,GA5GA,EA4GA;MAAA;;MACA;QACA;;QACA;UACA,6FADA,CAEA;;UACA,uCACA0B;YAAA;YAAA;YAAA;;YAAA;UAAA,EADA;;UAIA;YACA;YACA;UACA;QACA,CAXA,CAWA;UACAC;UACA;QACA;;QACA;MACA,CAlBA;IAmBA,CAhIA;IAiIAC,QAjIA,sBAiIA;MAAA;;MACA;;MACA;QACA;UACA;UACA;UACAC;UACA;;QACA;UACAA;UACA;;QACA;UACAA;UACA;;QACA;UACA;UACAC;UACAD;UACA;;QACA;UACA;YACAxB,iBADA;YAEAa;UAFA;UAIAW;UACA;;QACA;UACA;YACAxB,iBADA;YAEAa;UAFA;UAIAW;UACA;;QACA;UACAF;MAhCA;;MAkCAE;QACA;QACA;;QACA;;QACA;MACA,CALA;IAMA,CA3KA;IA4KAE,UA5KA,sBA4KA/B,GA5KA,EA4KA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAnLA;IAoLAgC,YApLA,wBAoLAhC,GApLA,EAoLA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CA3LA;IA4LAiC,WA5LA,uBA4LAjC,GA5LA,EA4LA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAnMA;IAoMAkC,mBApMA,+BAoMAlC,GApMA,EAoMA;MACA;MACA;MACA;IACA,CAxMA;IAyMAmC,aAzMA,yBAyMAnC,GAzMA,EAyMA;MACA;MACA;MACA;MACA;MACA;IAEA,CAhNA;IAiNAoC,WAjNA,uBAiNApC,GAjNA,EAiNA;MACA;MACA;MACA;MACA;MACA;IACA,CAvNA;IAwNAqC,YAxNA,wBAwNArC,GAxNA,EAwNA;MAAA;;MACA;QACA;MACA,CAFA,EAEAsC,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CA/NA;;IAgOA;IACAC,UAjOA,wBAiOA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAIA;;;QACA;UACA;YACA;;YACA;;YACA;UACA,CAJA;UAKA;QACA,CAZA,CAaA;;;QACA;UACA;;UACA;;UACA;QACA,CAJA;MAKA,CAnBA;IAoBA,CAtPA;;IAuPA;IACAC,YAxPA,wBAwPAzC,GAxPA,EAwPA;MAAA;;MACA;MACA;QACA;MACA,CAFA,EAEAsC,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CAhQA;;IAiQA;IACAG,YAlQA,0BAkQA;MAAA;;MACA;MACA;MACAC;MACAA;MACA;MACA,wEANA,CAOA;;MACA;QACA;QACA;MACA,CAHA,EAGAL,IAHA,CAGA;QACA;;QACA;MACA,CANA,EAMAC,KANA,CAMA,cANA;IAOA,CAjRA;IAkRAK,WAlRA,uBAkRA5C,GAlRA,EAkRA;MACA;MACA;QACA7C,uBADA;QAEA/C;MAFA;IAIA,CAxRA;IAyRAyI,WAzRA,uBAyRA7C,GAzRA,EAyRA;MACA;MACA;IACA,CA5RA;IA6RA8C,mBA7RA,+BA6RA9C,GA7RA,EA6RA;MACA;MACA;IACA,CAhSA;IAiSA+C,YAjSA,wBAiSA/C,GAjSA,EAiSA;MACAgD;IACA,CAnSA;IAoSAC,eApSA,2BAoSAjD,GApSA,EAoSA;MACA;MACA;QACA7C;MADA;IAGA,CAzSA;IA0SA+F,cA1SA,0BA0SAlD,GA1SA,EA0SA;MACA;QACAvC,qBADA;QAEAN;MAFA;IAIA,CA/SA;IAgTAgG,iBAhTA,6BAgTAnD,GAhTA,EAgTA;MACA;QACAzE,UADA;QAEA4B;MAFA;IAIA,CArTA;IAsTAiG,kCAtTA;IAuTAC,gCAvTA;IAwTAC,YAxTA,0BAwTA;MACA;MACA;MACA;IACA,CA5TA;IA6TAC,uBA7TA,qCA6TA;MACA;MACA;MACA;IACA,CAjUA;IAkUA;IACAC,wBAnUA,oCAmUAC,KAnUA,EAmUAC,IAnUA,EAmUAC,QAnUA,EAmUA;MACA;IACA,CArUA;IAsUA;IACAC,iBAvUA,6BAuUAC,QAvUA,EAuUAH,IAvUA,EAuUAC,QAvUA,EAuUA;MACA;QACA;QACA;MACA;;MACA;MACA;MACA;MACAG;MACAA;MACAA;MACAd;MACA;MACA;MACA;MACA;MACA;IACA,CAxVA;IAyVA;IACAe,cA1VA,4BA0VA;MACA;IACA,CA5VA;;IA6VA;IACAC,mBA9VA,+BA8VAhE,GA9VA,EA8VA;MAAA;;MACA;MACA;QACA;;QACA;UACA;;UACA;QACA;;QAEA;UACA,qDADA,CAEA;;UACA,iDACA0B;YAAA;YAAA;YAAA;;YAAA;UAAA,EADA;;UAIA;YACA;;YACA;UACA,CAVA,CAYA;;;UACA;UACA;QACA,CAfA,CAeA;UACAC;;UACA;QACA;MACA,CA1BA,EA0BAY,KA1BA,CA0BA;QACA;MACA,CA5BA;IA6BA,CA7XA;;IA8XA;IACA0B,yBA/XA,qCA+XAnI,IA/XA,EA+XA;MACA;IACA,CAjYA;;IAkYA;IACAoI,iBAnYA,+BAmYA;MACA;MACA;MACA;IACA,CAvYA;;IAwYA;IACAC,uBAzYA,qCAyYA;MACA;MACA;IACA,CA5YA;;IA6YA;IACAC,eA9YA,6BA8YA;MAAA;;MACA,mCADA,CAEA;;MACA;;MACA;QACAzB;QACAA;MACA;;MAEA;QACA;QACA;QACA;MACA,CAJA,EAIAJ,KAJA,CAIA;QACA;MACA,CANA;IAOA,CA9ZA;;IA+ZA;IACA8B,kBAhaA,gCAgaA;MACA;MACA;QACAzI,SADA;QAEAC,YAFA;QAGAsD,mBAHA;QAIAC,YAJA;QAKAC,kBALA;QAMAC,oBANA;QAOAC;MAPA;MASA;IACA,CA5aA;;IA6aA;IACA+E,yBA9aA,uCA8aA;MACA;IACA,CAhbA;;IAibA;IACAC,qBAlbA,iCAkbAnF,MAlbA,EAkbA;MACA;QACA;UACA;;QACA;UACA;;QACA;UACA;;QACA;UACA;MARA;IAUA,CA7bA;;IA8bA;IACAoF,cA/bA,0BA+bAC,QA/bA,EA+bA;MACA;MAEA;MACA;MACA;;MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA,CAFA,MAEA;QACA;MACA;IACA,CA7cA;;IA8cA;IACAC,iBA/cA,+BA+cA;MACA;MACA;IACA,CAldA;;IAmdA;IACAC,4BApdA,0CAodA;MACA;MACA;MACA,8CAHA,CAIA;;MACA;QACA;MACA;IACA,CA5dA;;IA6dA;IACAC,wBA9dA,sCA8dA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAKA;;;QACA;QACA;;QAEA;UACAC,yBADA;UAEAC,sBAFA;UAGAhJ,eAHA;UAIAiJ;QAJA,GAKAzC,IALA,CAKA;UACA;QACA,CAPA,EAOAC,KAPA,CAOA,aACA;QACA,CATA;MAUA,CAnBA;IAoBA,CAnfA;;IAofA;IACAyC,kBArfA,gCAqfA;MAAA;;MACA,sCADA,CAGA;;MACA;MACA;MACA;MAEA;QACAvF;MADA;MAIA;QACA;;QACA,uCAFA,CAIA;;;QACA;QAAA;QAAA;;QACA,2LANA,CAQA;;;QACA;MACA,CAVA,EAUA8C,KAVA,CAUA;QACA,0CADA,CAEA;;QACAZ;MACA,CAdA;IAeA;EAhhBA;AA3JA,C", "names": ["name", "components", "ImageUpload", "WorkOrderDetail", "ecard", "esign", "order", "household", "bankCard", "personInfo", "disable<PERSON><PERSON>", "CompanySelect", "data", "loading", "exportLoading", "showSearch", "total", "list", "title", "open", "dateRangeCompensatingDate", "dateRangeCreateTime", "dateRangeTreatmentDatetime", "queryParams", "pageNo", "pageSize", "type", "idCardNumber", "mobilePhoneNumber", "treatmentSerialNumberType", "completeStatus", "hospitalName", "companyId", "types", "form", "rules", "required", "message", "trigger", "detailId", "detailTitle", "detailOpen", "detailOpen2", "method", "ecardOpen", "esignOpen", "orderOpen", "idNum", "confirmBtnText", "confirmBtnLoading", "houseHoldOpen", "opType", "bankAccount", "bankAccountOpen", "hospitalNames", "upload", "isUploading", "headers", "url", "claimAmountUrl", "wandaImportUrl", "supplementary", "activeTab", "files", "supplementaryTypeMap", "MEDICAL_DIAGNOSIS_PROOF", "MEDICAL_FEE_INVOICE", "DRUG_LIST", "MEDICAL_SETTLEMENT", "OUTPATIENT_MEDICAL_RECORDS", "DISCHARGE_RECORD", "DISABILITY_CERTIFICATE", "TRAFFIC_ACCIDENT_CERTIFICATE", "wandaImportLoading", "detailForm", "fileUrlMap", "batchLogDrawer", "visible", "date<PERSON><PERSON><PERSON>", "operationType", "status", "operatorName", "beginStartTime", "endStartTime", "batchRejectDialog", "cutoffDate", "created", "methods", "canInitialFilter", "handleCommand", "command", "index", "row", "getList", "getStatus", "cancel", "reset", "id", "address", "invoice", "bill", "medicalRecord", "summary", "diagnose", "disabilityReport", "deathProof", "adviceMoney", "suggestCompensatingMoney", "actualMoney", "compensatingDate", "remark", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleUpdate", "handleCreatePdf", "handleDetail", "handleDetail2", "Object", "console", "doAction", "p", "submitData", "handleTake", "handleReject", "handleDelay", "handleHospitalCheck", "handleProcess", "handleVisit", "handleReturn", "then", "catch", "submitForm", "handleDelete", "handleExport", "params", "handleEcard", "handleOrder", "handleDisablePerson", "handleRecord", "window", "handleHouseHold", "handleBankCard", "handlerPersonInfo", "check<PERSON><PERSON><PERSON>", "checkRole", "handleImport", "handleClaimAmountImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "response", "downA", "submitFileForm", "handleSupplementary", "getSupplementaryTypeLabel", "handleWandaImport", "handleBatchOperationLog", "getBatchLogList", "resetBatchLog<PERSON>uery", "handleBatchLogDrawerClose", "getBatchLogStatusType", "formatDuration", "duration", "handleBatchReject", "handleBatchRejectDialogClose", "handleBatchRejectConfirm", "confirmButtonText", "cancelButtonText", "dangerouslyUseHTMLString", "executeBatchReject"], "sourceRoot": "src/views/insurance/workOrder", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"90px\">\r\n      <el-form-item label=\"保险类型\">\r\n        <el-select v-model=\"queryParams.type\" placeholder=\"请选择保险类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\" v-if=\"(dict.value == '1' && checkPermi(['insurance:old-people-accident-insurance:query'])) || ((dict.value == '7' || dict.value == '8' || dict.value == '9') && checkPermi(['insurance:disabled-people-insurance:query']))\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"医院名称\">\r\n        <el-select v-model=\"queryParams.hospitalName\" placeholder=\"请选择医院\" clearable>\r\n          <el-option\r\n            v-for=\"item in hospitalNames\"\r\n            :key=\"item\"\r\n            :label=\"item\"\r\n            :value=\"item\">\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"保险公司\" v-if=\"checkPermi(['insurance:company:query'])\">\r\n        <CompanySelect v-model=\"queryParams.companyId\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"长者名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入长者名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"身份证号\" prop=\"idCardNumber\">\r\n        <el-input v-model=\"queryParams.idCardNumber\" placeholder=\"请输入身份证号\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"mobilePhoneNumber\">\r\n        <el-input v-model=\"queryParams.mobilePhoneNumber\" placeholder=\"请输入联系方式\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"理赔时间\">\r\n        <el-date-picker v-model=\"dateRangeCompensatingDate\" style=\"width: 240px\" value-format=\"yyyy-MM-dd\"\r\n                        type=\"daterange\" range-separator=\"-\" start-placeholder=\"开始日期\" end-placeholder=\"结束日期\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"工单类型\">\r\n        <el-select v-model=\"queryParams.treatmentSerialNumberType\" placeholder=\"请选择工单类型\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_TREATMENT_TYPE)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"票据完整度\">\r\n        <el-select v-model=\"queryParams.completeStatus\" placeholder=\"请选择完整度\" clearable>\r\n          <el-option v-for=\"dict in this.getDictDatas(DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS)\"\r\n                     :key=\"dict.value\" :label=\"dict.label\" :value=\"dict.value\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"就诊时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeTreatmentDatetime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRangeCreateTime\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:work-order:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:export']\">导出</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleImport\"\r\n                   v-hasPermi=\"['insurance:work-order:settlement-import']\">已理赔工单导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" icon=\"el-icon-upload2\" size=\"mini\" @click=\"handleClaimAmountImport\"\r\n                   v-hasPermi=\"['insurance:work-order:claim-import']\">理赔金额导入</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"info\" size=\"mini\" icon=\"el-icon-upload2\"\r\n                   @click=\"handleWandaImport\" :loading=\"wandaImportLoading\"\r\n                   v-hasPermi=\"['insurance:work-order:import']\">万达医疗数据更新</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"success\" plain icon=\"el-icon-document\" size=\"mini\"\r\n                   @click=\"handleBatchOperationLog\"\r\n                   v-hasPermi=\"['insurance:work-order:query']\">批量操作日志</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\"\r\n                   @click=\"handleBatchReject\"\r\n                   v-hasPermi=\"['insurance:work-order:batch-reject']\">批量拒绝旧工单</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"编号\" align=\"center\" type=\"index\" width=\"50px\"/>\r\n      <el-table-column label=\"保险类型\" align=\"center\" prop=\"type\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"工单类型\" align=\"center\" prop=\"treatmentSerialNumberType\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TREATMENT_TYPE\" :value=\"scope.row.treatmentSerialNumberType\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column v-if=\"checkPermi(['insurance:work-order:show-company'])\" label=\"负责公司\" align=\"center\" prop=\"companyName\" />\r\n      <el-table-column label=\"长者名称\" align=\"center\" prop=\"desensitizedName\" />\r\n      <el-table-column label=\"身份证号\" align=\"center\" prop=\"desensitizedIdCardNumber\" />\r\n      <el-table-column label=\"联系方式\" align=\"center\" prop=\"desensitizedMobilePhoneNumber\" />\r\n      <el-table-column label=\"医院\" align=\"center\" prop=\"hospitalName\" />\r\n      <el-table-column label=\"就诊时间\" align=\"center\" prop=\"treatmentDatetime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.treatmentDatetime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建议理赔金额\" align=\"center\" width=\"100\" prop=\"suggestCompensatingMoney\" />\r\n      <el-table-column label=\"赔付金额\" align=\"center\" prop=\"actualMoney\" />\r\n      <el-table-column label=\"票据完整度\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_WORK_ORDER_TICKET_COMPLETE_STATUS\" :value=\"scope.row.completeStatus\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"险种\" align=\"center\" prop=\"completeStatus\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :type=\"DICT_TYPE.INSURANCE_TYPE\" :value=\"scope.row.type\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"授权状态\" align=\"center\" prop=\"authStatus\" />\r\n      <el-table-column label=\"理赔申请\" align=\"center\" prop=\"supplementaryFileRecordId\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.supplementaryFileRecordId == null\">否</el-tag>\r\n          <el-tag v-else type=\"info\">是</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column fixed=\"right\" label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"300px\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleEcard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:cert'])\">电子证照</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleOrder(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:ticket'])\">电子保单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHouseHold(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:house'])\">电子户口</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleBankCard(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:elec:bankcard'])\">银行账号</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handlerPersonInfo(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">联系方式</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDisablePerson(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">残疾人证</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleRecord(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:medical']) && scope.row.status > 1\">就诊证明</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"if(!scope.row.supplementaryFileRecordId) { handleDetail(scope.row); } else { handleDetail2(scope.row); }\"\r\n                    v-if=\"checkPermi(['insurance:work-order:query'])\">详情</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleTake(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:take']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">接单</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReject(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:reject']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">拒绝</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleDelay(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:delay']) && scope.row.status === 0 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">延后</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleHospitalCheck(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:hospital-check']) && scope.row.status === 1 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">盖章</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleProcess(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:process']) && scope.row.status === 2 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">处理</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleVisit(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:visit']) && scope.row.status === 3 && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回访</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleReturn(scope.row)\"\r\n                    v-if=\"checkPermi(['insurance:work-order:return']) && (scope.row.status === 2 || scope.row.status === 3 || scope.row.status === 6) && queryParams.status != null && ((scope.row.type === 1 && checkPermi(['insurance:old-people-accident-insurance:update'])) || ((scope.row.type === 7 || scope.row.type === 8 || scope.row.type === 9) && checkPermi(['insurance:disabled-people-insurance:update'])))\">回退</el-button>\r\n          <el-button size=\"mini\" type=\"text\" @click=\"handleSupplementary(scope.row)\"\r\n                     v-if=\"checkPermi(['insurance:work-order:query'])\">补充资料</el-button>\r\n          <el-dropdown  @command=\"(command) => handleCommand(command, scope.$index, scope.row)\"\r\n                        v-if=\"queryParams.status != null\"\r\n                        v-hasPermi=\"['insurance:work-order:update', 'insurance:work-order:delete']\">\r\n                <span class=\"el-dropdown-link\">\r\n                  <i class=\"el-icon-d-arrow-right el-icon--right\"></i>更多\r\n                </span>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDelete\" size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                                v-hasPermi=\"['insurance:work-order:delete']\">删除</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleCreatePdf\" size=\"mini\" type=\"text\" icon=\"el-icon-edit\"\r\n                                v-hasPermi=\"['insurance:work-order:update']\">创建未签章pdf</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <el-drawer :title=\"detailTitle\" :visible.sync=\"detailOpen\" direction=\"rtl\" size=\"60%\">\r\n      <WorkOrderDetail ref=\"workOrderDetail\" :opType=\"opType\" v-if=\"detailOpen\" :id=\"detailId\" />\r\n      <div class=\"drawer-footer\" v-if=\"detailTitle !== '查看详情'\">\r\n        <el-button type=\"primary\" @click=\"doAction\" :loading=\"confirmBtnLoading\">{{confirmBtnText}}</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-drawer title=\"电子签章\" :visible.sync=\"esignOpen\" direction=\"rtl\" size=\"90%\">\r\n      <esign />\r\n      <div class=\"drawer-footer\">\r\n        <el-button type=\"primary\" @click=\"doAction\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子证照\" :visible.sync=\"ecardOpen\" width=\"500px\">\r\n      <ecard :idNum=\"ecard.idNum\" :name=\"ecard.name\" />\r\n    </el-dialog>\r\n    <el-drawer title=\"保单详情\" :visible.sync=\"orderOpen\" direction=\"rtl\" size=\"90%\">\r\n      <order :idNum=\"idNum\"/>\r\n    </el-drawer>\r\n    <el-drawer title=\"残疾人证\" :visible.sync=\"disablePerson.open\" direction=\"rtl\" size=\"90%\">\r\n      <disablePerson :idNum=\"disablePerson.idNum\"/>\r\n    </el-drawer>\r\n    <el-dialog title=\"电子户口\" :visible.sync=\"houseHoldOpen\" width=\"550px\">\r\n      <household :idNum=\"household.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"银行账号\" :visible.sync=\"bankAccount.bankAccountOpen\" width=\"550px\">\r\n      <bankCard :idCard=\"bankAccount.idNum\" />\r\n    </el-dialog>\r\n    <el-dialog title=\"多来源信息\" :visible.sync=\"personInfo.open\" width=\"550px\">\r\n      <personInfo :idCard=\"personInfo.idNum\" />\r\n    </el-dialog>\r\n\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload ref=\"upload\" :limit=\"1\" accept=\".xlsx, .xls\" :headers=\"upload.headers\"\r\n        :action=\"upload.url\" :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\" :on-success=\"handleFileSuccess\" :auto-upload=\"false\" drag>\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"补充资料\" :visible.sync=\"supplementary.open\" width=\"800px\" append-to-body>\r\n      <el-tabs v-model=\"supplementary.activeTab\">\r\n        <el-tab-pane v-for=\"(urls, type) in supplementary.files\"\r\n                     :key=\"type\"\r\n                     :label=\"getSupplementaryTypeLabel(type)\"\r\n                     :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 详情对话框 -->\r\n    <el-dialog :title=\"'补充资料详情'\" :visible.sync=\"detailOpen2\" width=\"800px\" append-to-body>\r\n      <el-descriptions :column=\"2\" border>\r\n        <el-descriptions-item label=\"居民\">{{ detailForm.residentName }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"状态\">\r\n          <dict-tag :type=\"DICT_TYPE.SUPPLEMENTARY_RECORD_STATUS\" :value=\"detailForm.status\"/>\r\n        </el-descriptions-item>\r\n        <el-descriptions-item label=\"户籍地址\">{{ detailForm.hjAddress }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"手机号\">{{ detailForm.phone }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"实际理赔金额\" v-if=\"detailForm.actualMoney\">\r\n          {{ detailForm.actualMoney }}元\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <el-divider content-position=\"left\">补充资料</el-divider>\r\n      <el-tabs v-model=\"activeTab\">\r\n        <el-tab-pane\r\n          v-for=\"(urls, type) in fileUrlMap\"\r\n          :key=\"type\"\r\n          :label=\"getSupplementaryTypeLabel(type)\"\r\n          :name=\"type\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\" v-for=\"(url, index) in urls\" :key=\"index\">\r\n              <el-image\r\n                style=\"width: 100%; height: 200px\"\r\n                :src=\"url\"\r\n                :preview-src-list=\"urls\">\r\n              </el-image>\r\n            </el-col>\r\n          </el-row>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-dialog>\r\n\r\n    <!-- 批量操作日志抽屉 -->\r\n    <el-drawer\r\n      title=\"批量操作日志\"\r\n      :visible.sync=\"batchLogDrawer.visible\"\r\n      direction=\"rtl\"\r\n      size=\"60%\"\r\n      :before-close=\"handleBatchLogDrawerClose\">\r\n\r\n      <!-- 搜索条件 -->\r\n      <el-form :model=\"batchLogDrawer.queryParams\" ref=\"batchLogQueryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\" style=\"margin-bottom: 20px;\">\r\n        <el-form-item label=\"操作类型\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.operationType\" placeholder=\"请选择操作类型\" clearable>\r\n            <el-option label=\"批量拒绝\" value=\"BATCH_REJECT\"></el-option>\r\n            <el-option label=\"批量恢复\" value=\"BATCH_RECOVER\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作状态\">\r\n          <el-select v-model=\"batchLogDrawer.queryParams.status\" placeholder=\"请选择状态\" clearable>\r\n            <el-option label=\"执行中\" value=\"RUNNING\"></el-option>\r\n            <el-option label=\"已完成\" value=\"COMPLETED\"></el-option>\r\n            <el-option label=\"执行失败\" value=\"FAILED\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作员\">\r\n          <el-input v-model=\"batchLogDrawer.queryParams.operatorName\" placeholder=\"请输入操作员姓名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"操作时间\">\r\n          <el-date-picker\r\n            v-model=\"batchLogDrawer.dateRange\"\r\n            style=\"width: 240px\"\r\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            type=\"datetimerange\"\r\n            range-separator=\"-\"\r\n            start-placeholder=\"开始时间\"\r\n            end-placeholder=\"结束时间\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getBatchLogList\">搜索</el-button>\r\n          <el-button icon=\"el-icon-refresh\" @click=\"resetBatchLogQuery\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 批量操作日志表格 -->\r\n      <el-table v-loading=\"batchLogDrawer.loading\" :data=\"batchLogDrawer.list\" style=\"width: 100%\">\r\n        <el-table-column label=\"批次号\" align=\"center\" prop=\"batchId\" width=\"180\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column label=\"操作类型\" align=\"center\" prop=\"operationTypeDisplay\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"statusDisplay\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getBatchLogStatusType(scope.row.status)\">{{ scope.row.statusDisplay }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"处理数量\" align=\"center\" prop=\"processedCount\" width=\"80\"></el-table-column>\r\n        <el-table-column label=\"操作员\" align=\"center\" prop=\"operatorName\" width=\"100\"></el-table-column>\r\n        <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.endTime ? parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"执行时长\" align=\"center\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ formatDuration(scope.row.duration) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remarks\" show-overflow-tooltip></el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <pagination\r\n        v-show=\"batchLogDrawer.total > 0\"\r\n        :total=\"batchLogDrawer.total\"\r\n        :page.sync=\"batchLogDrawer.queryParams.pageNo\"\r\n        :limit.sync=\"batchLogDrawer.queryParams.pageSize\"\r\n        @pagination=\"getBatchLogList\"\r\n        style=\"margin-top: 20px;\" />\r\n    </el-drawer>\r\n\r\n    <!-- 批量拒绝模态框 -->\r\n    <el-dialog\r\n      title=\"批量拒绝历史待接单工单\"\r\n      :visible.sync=\"batchRejectDialog.visible\"\r\n      width=\"500px\"\r\n      :before-close=\"handleBatchRejectDialogClose\">\r\n\r\n      <div style=\"margin-bottom: 20px;\">\r\n        <p style=\"color: #606266; line-height: 1.6;\">\r\n          此操作将批量拒绝指定日期及之前的所有待接单工单。被拒绝的工单状态将变更为\"行政拒绝\"，\r\n          此操作可通过批量操作日志进行恢复。请谨慎操作。\r\n        </p>\r\n      </div>\r\n\r\n      <el-form :model=\"batchRejectDialog.form\" ref=\"batchRejectForm\" label-width=\"120px\">\r\n        <el-form-item label=\"截止日期\" prop=\"cutoffDate\"\r\n                      :rules=\"[{ required: true, message: '请选择截止日期', trigger: 'change' }]\">\r\n          <el-date-picker\r\n            v-model=\"batchRejectDialog.form.cutoffDate\"\r\n            type=\"date\"\r\n            placeholder=\"选择截止日期\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            style=\"width: 100%\">\r\n          </el-date-picker>\r\n          <div style=\"font-size: 12px; color: #909399; margin-top: 5px;\">\r\n            将拒绝此日期及之前的所有待接单工单\r\n          </div>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"handleBatchRejectDialogClose\">取 消</el-button>\r\n        <el-button type=\"danger\" @click=\"handleBatchRejectConfirm\" :loading=\"batchRejectDialog.loading\">执 行</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  createWorkOrder,\r\n  updateWorkOrder,\r\n  deleteWorkOrder,\r\n  getWorkOrder,\r\n  getWorkOrderPage,\r\n  exportWorkOrderExcel,\r\n  takeWorkOrder,\r\n  hospitalCheck,\r\n  process as process2,\r\n  visit,\r\n  reject,\r\n  delay,\r\n  returnWorkOrder,\r\n  getHospitalNames,\r\n  createPdf,\r\n  getBatchOperationLogPage,\r\n  batchRejectWorkOrders\r\n}\r\nfrom \"@/api/insurance/workOrder\";\r\nimport {getSupplementaryFileRecord} from \"@/api/insurance/supplementaryFileRecord\";\r\nimport ImageUpload from '@/components/ImageUpload';\r\nimport { checkPermi, checkRole } from \"@/utils/permission\";\r\nimport WorkOrderDetail from \"./detail\"\r\nimport ecard from '../components/ecard.vue';\r\nimport esign from '../components/esignature.vue';\r\nimport order from '../components/order.vue';\r\nimport disablePerson from '../components/disablePerson.vue';\r\nimport household from '../components/household.vue';\r\nimport bankCard from '../components/bankCard.vue';\r\nimport personInfo from '../components/personInfo.vue';\r\nimport CompanySelect from '../company/components/companySelect.vue';\r\nimport {getBaseHeader} from \"@/utils/request\";\r\nconst CONFIRM_TEXT = '确 定';\r\nexport default {\r\n  name: \"WorkOrder\",\r\n  components: {\r\n    ImageUpload,\r\n    WorkOrderDetail,\r\n    ecard,\r\n    esign,\r\n    order,\r\n    household,\r\n    bankCard,\r\n    personInfo,\r\n    disablePerson,\r\n    CompanySelect,\r\n  },\r\n  data() {\r\n\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 工单列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      dateRangeCompensatingDate: [],\r\n      dateRangeCreateTime: [],\r\n      dateRangeTreatmentDatetime: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        type: null,\r\n        name: null,\r\n        idCardNumber: null,\r\n        mobilePhoneNumber: null,\r\n        treatmentSerialNumberType: this.canInitialFilter()? '1': null,\r\n        completeStatus: this.canInitialFilter()? '0': null,\r\n        hospitalName: null,\r\n        companyId: null,\r\n        types: [1, 7, 8, 9],\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"长者名称不能为空\", trigger: \"blur\" }],\r\n        idCardNumber: [{ required: true, message: \"身份证号不能为空\", trigger: \"blur\" }],\r\n      },\r\n      detailId: undefined,\r\n      detailTitle: undefined,\r\n      detailOpen: false,\r\n      detailOpen2: false,\r\n      method: undefined,\r\n      ecardOpen: false,\r\n      esignOpen: false,\r\n      orderOpen: false,\r\n      disablePerson: {\r\n        open: false,\r\n        idNum: undefined,\r\n      },\r\n      ecard: {\r\n        idNum: undefined,\r\n        name: undefined\r\n      },\r\n      confirmBtnText: CONFIRM_TEXT,\r\n      confirmBtnLoading: false,\r\n      houseHoldOpen: false,\r\n      household: {\r\n        idNum: undefined,\r\n      },\r\n      opType: undefined,\r\n      idNum: undefined,\r\n      bankAccount: {\r\n        bankAccountOpen: false,\r\n        idNum: undefined\r\n      },\r\n      personInfo: {\r\n        open: false,\r\n        idNum: undefined\r\n      },\r\n      hospitalNames: [],\r\n      //已赔付工单导入\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 设置上传的请求头部\r\n        headers: getBaseHeader(),\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-settlement-work-order',\r\n        claimAmountUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-claim-amount-work-order',\r\n        wandaImportUrl: process.env.VUE_APP_BASE_API + '/admin-api/insurance/work-order/import-wanda-data'\r\n      },\r\n      supplementary: {\r\n        open: false,\r\n        activeTab: '',\r\n        files: {},\r\n      },\r\n      supplementaryTypeMap: {\r\n        MEDICAL_DIAGNOSIS_PROOF: '医疗诊断证明',\r\n        MEDICAL_FEE_INVOICE: '医疗费用发票',\r\n        DRUG_LIST: '用药清单',\r\n        MEDICAL_SETTLEMENT: '医保结算单',\r\n        OUTPATIENT_MEDICAL_RECORDS: '门诊病历(门诊)',\r\n        DISCHARGE_RECORD: '出院记录(住院)',\r\n        DISABILITY_CERTIFICATE: '残疾鉴定报告',\r\n        TRAFFIC_ACCIDENT_CERTIFICATE: '交通事故责任认定书'\r\n      },\r\n      wandaImportLoading: false,\r\n      detailForm: {},\r\n      fileUrlMap: {},\r\n      activeTab: '',\r\n      // 批量操作日志抽屉\r\n      batchLogDrawer: {\r\n        visible: false,\r\n        loading: false,\r\n        total: 0,\r\n        list: [],\r\n        dateRange: [],\r\n        queryParams: {\r\n          pageNo: 1,\r\n          pageSize: 10,\r\n          operationType: null,\r\n          status: null,\r\n          operatorName: null,\r\n          beginStartTime: null,\r\n          endStartTime: null\r\n        }\r\n      },\r\n      // 批量拒绝对话框\r\n      batchRejectDialog: {\r\n        visible: false,\r\n        loading: false,\r\n        form: {\r\n          cutoffDate: null\r\n        }\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    getHospitalNames().then(response => {\r\n      this.hospitalNames = response.data;\r\n    })\r\n  },\r\n  methods: {\r\n    canInitialFilter() {\r\n      // return this.checkRole(['insurance', 'mz']);\r\n      return false;\r\n    },\r\n    handleCommand(command, index, row) {\r\n      switch (command) {\r\n        case 'handleUpdate':\r\n          this.handleUpdate(row);\r\n          break;\r\n        case 'handleDelete':\r\n          this.handleDelete(row);\r\n          break;\r\n        case 'handleCreatePdf':\r\n          this.handleCreatePdf(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      this.queryParams.status = this.getStatus();\r\n      let params = {...this.queryParams};\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行查询\r\n      getWorkOrderPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getStatus() {\r\n      return this.$route.query.status\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.detailOpen = false;\r\n      this.detailOpen2 = false;\r\n      this.esignOpen = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        idCardNumber: undefined,\r\n        mobilePhoneNumber: undefined,\r\n        address: undefined,\r\n        hospitalName: undefined,\r\n        invoice: undefined,\r\n        bill: undefined,\r\n        medicalRecord: undefined,\r\n        summary: undefined,\r\n        diagnose: undefined,\r\n        disabilityReport: undefined,\r\n        deathProof: undefined,\r\n        adviceMoney: undefined,\r\n        suggestCompensatingMoney: undefined,\r\n        actualMoney: undefined,\r\n        compensatingDate: undefined,\r\n        remark: undefined,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRangeCompensatingDate = [];\r\n      this.dateRangeCreateTime = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加工单\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getWorkOrder(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改工单\";\r\n      });\r\n    },\r\n    handleCreatePdf(row) {\r\n      createPdf(row.id).then(response => {\r\n        this.getList();\r\n      });\r\n    },\r\n    handleDetail(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"查看详情\";\r\n    },\r\n    handleDetail2(row) {\r\n      getSupplementaryFileRecord(row.supplementaryFileRecordId).then(response => {\r\n        this.detailForm = response.data;\r\n        try {\r\n          this.fileUrlMap = this.detailForm.fileUrls ? JSON.parse(this.detailForm.fileUrls) : {};\r\n          // 过滤掉没有图片的类型\r\n          this.fileUrlMap = Object.fromEntries(\r\n            Object.entries(this.fileUrlMap).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.fileUrlMap).length > 0) {\r\n            // 设置第一个标签为激活状态\r\n            this.activeTab = Object.keys(this.fileUrlMap)[0];\r\n          }\r\n        } catch (e) {\r\n          console.error('解析 fileUrls 失败:', e);\r\n          this.fileUrlMap = {};\r\n        }\r\n        this.detailOpen2 = true;\r\n      });\r\n    },\r\n    doAction() {\r\n      let p = undefined;\r\n      switch(this.method) {\r\n        case 'take':\r\n          this.confirmBtnText = '正在签章,请稍候';\r\n          this.confirmBtnLoading = true;\r\n          p = takeWorkOrder(this.detailId);\r\n          break;\r\n        case 'hospital':\r\n          p = hospitalCheck(this.detailId);\r\n          break;\r\n        case 'process':\r\n          p = process2(this.detailId);\r\n          break;\r\n        case 'visit':\r\n          let submitData = this.$refs.workOrderDetail.getSubmitData()\r\n          submitData.id = this.detailId\r\n          p = visit(submitData);\r\n          break;\r\n        case 'reject':\r\n          let rejectData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = reject(rejectData);\r\n          break;\r\n        case 'delay':\r\n          let delayData = {\r\n            id: this.detailId,\r\n            remark: this.$refs.workOrderDetail.getSubmitData().remark\r\n          }\r\n          p = delay(delayData);\r\n          break;\r\n        default:\r\n          console.log('找不到对应方法: ' + this.method);\r\n      }\r\n      p.then(() => {\r\n        this.confirmBtnLoading = false;\r\n        this.confirmBtnText = CONFIRM_TEXT;\r\n        this.cancel();\r\n        this.getList();\r\n      });\r\n    },\r\n    handleTake(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"接单\";\r\n      this.method = 'take';\r\n      this.confirmBtnText = '接 单';\r\n      this.opType = 'take';\r\n    },\r\n    handleReject(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"拒绝\";\r\n      this.method = 'reject';\r\n      this.confirmBtnText = '拒绝';\r\n      this.opType = 'reject';\r\n    },\r\n    handleDelay(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"延后\";\r\n      this.method = 'delay';\r\n      this.confirmBtnText = '延 后';\r\n      this.opType = 'delay';\r\n    },\r\n    handleHospitalCheck(row) {\r\n      this.detailId = row.id;\r\n      this.esignOpen = true;\r\n      this.method = 'hospital';\r\n    },\r\n    handleProcess(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"处理\";\r\n      this.method = 'process';\r\n      this.opType = 'process';\r\n\r\n    },\r\n    handleVisit(row) {\r\n      this.detailId = row.id;\r\n      this.detailOpen = true;\r\n      this.detailTitle = \"回访\";\r\n      this.method = 'visit';\r\n      this.opType = 'visit';\r\n    },\r\n    handleReturn(row) {\r\n      this.$modal.confirm(`是否确认回退工单(${row.name})`).then(function() {\r\n          return returnWorkOrder(row.id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"回退成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateWorkOrder(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createWorkOrder(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除工单编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteWorkOrder(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      this.addBeginAndEndTime(params, this.dateRangeCompensatingDate, 'compensatingDate');\r\n      this.addBeginAndEndTime(params, this.dateRangeCreateTime, 'createTime');\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有工单数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportWorkOrderExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '工单.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleEcard(row) {\r\n      this.ecardOpen = true;\r\n      this.ecard = {\r\n        idNum: row.idCardNumber,\r\n        name: row.name\r\n      }\r\n    },\r\n    handleOrder(row) {\r\n      this.orderOpen = true;\r\n      this.idNum = row.idCardNumber\r\n    },\r\n    handleDisablePerson(row) {\r\n      this.disablePerson.open = true;\r\n      this.disablePerson.idNum = row.idCardNumber;\r\n    },\r\n    handleRecord(row) {\r\n      window.open(row.pdf.signedPdf, \"_blank\", \"resizable,scrollbars,status\");\r\n    },\r\n    handleHouseHold(row) {\r\n      this.houseHoldOpen = true;\r\n      this.household = {\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handleBankCard(row) {\r\n      this.bankAccount = {\r\n        bankAccountOpen: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    handlerPersonInfo(row) {\r\n      this.personInfo = {\r\n        open: true,\r\n        idNum: row.idCardNumber,\r\n      }\r\n    },\r\n    checkPermi,\r\n    checkRole,\r\n    handleImport() {\r\n      this.upload.title = \"工单导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.url;\r\n    },\r\n    handleClaimAmountImport() {\r\n      this.upload.title = \"理赔金额导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.claimAmountUrl;\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      if (response.code !== 0) {\r\n        this.$modal.msgError(response.msg)\r\n        return;\r\n      }\r\n      let fileName = file.name;\r\n      let href = response.data;\r\n      let downA = document.createElement(\"a\");\r\n      downA.href = href;\r\n      downA.download = fileName;\r\n      downA.click();\r\n      window.URL.revokeObjectURL(href);\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$modal.msgSuccess(\"导入成功，请查看导入结果文件\");\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    },\r\n    /** 处理补充资料按钮点击 */\r\n    handleSupplementary(row) {\r\n      // 先获取工单详情\r\n      getWorkOrder(row.id).then(response => {\r\n        const workOrder = response.data;\r\n        if (!workOrder.supplementaryFiles) {\r\n          this.$modal.msgError(\"没有补充资料\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          const files = JSON.parse(workOrder.supplementaryFiles);\r\n          // 过滤掉没有图片的类型\r\n          this.supplementary.files = Object.fromEntries(\r\n            Object.entries(files).filter(([_, urls]) => urls && urls.length > 0)\r\n          );\r\n\r\n          if (Object.keys(this.supplementary.files).length === 0) {\r\n            this.$modal.msgError(\"没有补充资料\");\r\n            return;\r\n          }\r\n\r\n          // 设置第一个标签为激活状态\r\n          this.supplementary.activeTab = Object.keys(this.supplementary.files)[0];\r\n          this.supplementary.open = true;\r\n        } catch (e) {\r\n          console.error(\"解析补充资料失败\", e);\r\n          this.$modal.msgError(\"解析补充资料失败\");\r\n        }\r\n      }).catch(() => {\r\n        this.$modal.msgError(\"获取工单详情失败\");\r\n      });\r\n    },\r\n    /** 获取补充资料类型的显示文本 */\r\n    getSupplementaryTypeLabel(type) {\r\n      return this.supplementaryTypeMap[type] || type;\r\n    },\r\n    /** 万达数据导入按钮操作 */\r\n    handleWandaImport() {\r\n      this.upload.title = \"万达数据导入\";\r\n      this.upload.open = true;\r\n      this.upload.url = this.upload.wandaImportUrl;\r\n    },\r\n    /** 批量操作日志按钮操作 */\r\n    handleBatchOperationLog() {\r\n      this.batchLogDrawer.visible = true;\r\n      this.getBatchLogList();\r\n    },\r\n    /** 获取批量操作日志列表 */\r\n    getBatchLogList() {\r\n      this.batchLogDrawer.loading = true;\r\n      // 处理时间范围参数\r\n      let params = { ...this.batchLogDrawer.queryParams };\r\n      if (this.batchLogDrawer.dateRange && this.batchLogDrawer.dateRange.length === 2) {\r\n        params.beginStartTime = this.batchLogDrawer.dateRange[0];\r\n        params.endStartTime = this.batchLogDrawer.dateRange[1];\r\n      }\r\n\r\n      getBatchOperationLogPage(params).then(response => {\r\n        this.batchLogDrawer.list = response.data.list;\r\n        this.batchLogDrawer.total = response.data.total;\r\n        this.batchLogDrawer.loading = false;\r\n      }).catch(() => {\r\n        this.batchLogDrawer.loading = false;\r\n      });\r\n    },\r\n    /** 重置批量操作日志查询 */\r\n    resetBatchLogQuery() {\r\n      this.batchLogDrawer.dateRange = [];\r\n      this.batchLogDrawer.queryParams = {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        operationType: null,\r\n        status: null,\r\n        operatorName: null,\r\n        beginStartTime: null,\r\n        endStartTime: null\r\n      };\r\n      this.getBatchLogList();\r\n    },\r\n    /** 关闭批量操作日志抽屉 */\r\n    handleBatchLogDrawerClose() {\r\n      this.batchLogDrawer.visible = false;\r\n    },\r\n    /** 获取批量操作状态标签类型 */\r\n    getBatchLogStatusType(status) {\r\n      switch (status) {\r\n        case 'RUNNING':\r\n          return 'warning';\r\n        case 'COMPLETED':\r\n          return 'success';\r\n        case 'FAILED':\r\n          return 'danger';\r\n        default:\r\n          return 'info';\r\n      }\r\n    },\r\n    /** 格式化执行时长 */\r\n    formatDuration(duration) {\r\n      if (!duration) return '-';\r\n\r\n      const seconds = Math.floor(duration / 1000);\r\n      const minutes = Math.floor(seconds / 60);\r\n      const hours = Math.floor(minutes / 60);\r\n\r\n      if (hours > 0) {\r\n        return `${hours}小时${minutes % 60}分${seconds % 60}秒`;\r\n      } else if (minutes > 0) {\r\n        return `${minutes}分${seconds % 60}秒`;\r\n      } else {\r\n        return `${seconds}秒`;\r\n      }\r\n    },\r\n    /** 批量拒绝按钮操作 */\r\n    handleBatchReject() {\r\n      this.batchRejectDialog.visible = true;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n    },\r\n    /** 关闭批量拒绝对话框 */\r\n    handleBatchRejectDialogClose() {\r\n      this.batchRejectDialog.visible = false;\r\n      this.batchRejectDialog.loading = false;\r\n      this.batchRejectDialog.form.cutoffDate = null;\r\n      // 清除表单验证\r\n      if (this.$refs.batchRejectForm) {\r\n        this.$refs.batchRejectForm.clearValidate();\r\n      }\r\n    },\r\n    /** 批量拒绝确认操作 */\r\n    handleBatchRejectConfirm() {\r\n      this.$refs.batchRejectForm.validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n\r\n        // 二次确认对话框\r\n        const cutoffDate = this.batchRejectDialog.form.cutoffDate;\r\n        const confirmMessage = `您确定要拒绝 ${cutoffDate} 及之前的所有待接单工单吗？此操作可通过日志恢复。`;\r\n\r\n        this.$modal.confirm(confirmMessage, '批量拒绝确认', {\r\n          confirmButtonText: '确定执行',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n          dangerouslyUseHTMLString: false\r\n        }).then(() => {\r\n          this.executeBatchReject();\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        });\r\n      });\r\n    },\r\n    /** 执行批量拒绝操作 */\r\n    executeBatchReject() {\r\n      this.batchRejectDialog.loading = true;\r\n\r\n      // 将日期转换为时间戳（毫秒）\r\n      const cutoffDateStr = this.batchRejectDialog.form.cutoffDate + ' 23:59:59';\r\n      const cutoffDate = new Date(cutoffDateStr);\r\n      const cutoffTimestamp = cutoffDate.getTime();\r\n\r\n      const requestData = {\r\n        cutoffDate: cutoffTimestamp\r\n      };\r\n\r\n      batchRejectWorkOrders(requestData).then(response => {\r\n        this.batchRejectDialog.loading = false;\r\n        this.handleBatchRejectDialogClose();\r\n\r\n        // 显示成功提示\r\n        const { batchId, processedCount } = response.data;\r\n        this.$modal.msgSuccess(`操作成功！批次号：${batchId}，共处理了 ${processedCount} 条工单。`);\r\n\r\n        // 刷新列表\r\n        this.getList();\r\n      }).catch(error => {\r\n        this.batchRejectDialog.loading = false;\r\n        // 错误信息会由全局错误处理器显示\r\n        console.error('批量拒绝操作失败:', error);\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .drawer-footer {\r\n    display: flex;\r\n    padding: 0 50px 20px;\r\n    .el-button {\r\n      flex: 1\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}