
9308a7e83d0157f8f17298468c53d80688ef9d90	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"0e08706cc5159be3a6270b0e8804e0bd\"}","integrity":"sha512-MvW+cbxMSyTOJ//ZcQ8Xt+LaK8Bco79ej7QjN2/uY6UhrYyNwelqnnH5IgMAT/KHd337IATtNlMF4SqhwUlqLw==","time":1754357788307,"size":16761462}