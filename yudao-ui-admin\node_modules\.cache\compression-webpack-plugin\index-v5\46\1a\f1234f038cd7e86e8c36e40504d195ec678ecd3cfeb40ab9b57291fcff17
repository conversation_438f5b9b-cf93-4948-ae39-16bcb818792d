
4a7bf52a40b8c89f2089ba04ceb6ea3819588f7c	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"f97cb2fcc221de7e652f24767f22468c\"}","integrity":"sha512-fZ1SdoYRYA3vXDRzdgEKcCNdDG6zg8C5Mgg2yvyyUVqBZ6wjvfMSvCIAITdrkLb9wGiiAmVv1eH5t323tPVkUw==","time":1754356171784,"size":3474911}