
b4811d0ec73e4c8045ace67e8dde2a674354a304	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"5aa727263321c34c67e012c05cc40865\"}","integrity":"sha512-62KhDVbZL27lcEdnxXMLL6VXi8GVxkaS0O8DKGxczJtS6gEN3TutWpULzj8LIosRxPcLqkv4mKl9WR0LpU3Wtg==","time":1754360473752,"size":16761143}