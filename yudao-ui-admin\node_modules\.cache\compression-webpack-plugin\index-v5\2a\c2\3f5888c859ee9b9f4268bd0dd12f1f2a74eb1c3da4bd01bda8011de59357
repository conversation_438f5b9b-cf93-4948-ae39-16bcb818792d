
0d164b0b2e96241a3f82eb1e2e351607791ff83a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"191f0260e2c70a8b942f3d8508badca8\"}","integrity":"sha512-YJo2GKoBUevgwgS5wmEsk+7pHD+SriuqudMwpQxv/r11ejzbKVFjidUQ85NRgNlmHFT1m9k6dvtTtKZUTddOqA==","time":1754360022630,"size":3474561}