
f1ee2a9254ba032f0c015a7c888cea9f266481f0	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"a8351750fe9dde134caaae57790ea6d9\"}","integrity":"sha512-6Z9+WcADGTNu6s0OpRjO3xKNRJ/M09CCxP1RsyqOVr48S0ee/6TzczMydNWRuXVgRHhRE3KR7sStxulpuPNOlQ==","time":1754358555427,"size":3474322}