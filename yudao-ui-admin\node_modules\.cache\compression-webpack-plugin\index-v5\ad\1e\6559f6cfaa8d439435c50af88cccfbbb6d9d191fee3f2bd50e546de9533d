
d214544e6ea515aec43aecf6589ffc03129f9ff2	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"c2428eb34e76036ca8e458899816bcf4\"}","integrity":"sha512-WoJUskJkKKFeW1gP+6j4WwjQL1MGqDhxLa4FgrOqzK0/H7CmvzLBrJzvBO78kRChRQ7zxkLMPJsfsUHrMivBRA==","time":1754360974726,"size":3474348}