
0bd1ac6a3469f66959a8474b553cc3b6938546b2	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"6c708f38be3c8415c07f4ad1f42e7da9\"}","integrity":"sha512-mwAiY5sY1OJtqJ53wCv/OcOYXuCs0odPSvdT9tHsK+AUsVcXQ33kkqMgaoR5EP/8NqUlF1GyJyiijpHTI+CWag==","time":1754359295623,"size":16761354}