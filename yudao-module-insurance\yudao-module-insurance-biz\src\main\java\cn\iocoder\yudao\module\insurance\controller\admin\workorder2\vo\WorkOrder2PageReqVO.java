package cn.iocoder.yudao.module.insurance.controller.admin.workorder2.vo;

import com.deepoove.poi.config.Name;
import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@ApiModel("管理后台 - 工单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkOrder2PageReqVO extends PageParam {

    @ApiModelProperty(value = "医院代码")
    private String hospitalCode;

    @ApiModelProperty(value = "医院名称")
    private String hospitalName;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "身份证号码")
    private String idCardNumber;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "移动电话")
    private String mobilePhoneNumber;

    @ApiModelProperty(value = "社保卡号")
    private String socialMedicareCardNumber;

    @ApiModelProperty(value = "状态(0 待接单, 1 待盖章, 2 待处理, 3 待回访, 4 已完成)")
    private Integer status;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "开始创建时间")
    private Date beginCreateTime;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "结束创建时间")
    private Date endCreateTime;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "开始就诊时间")
    private Date beginTreatmentDatetime;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @ApiModelProperty(value = "结束就诊时间")
    private Date endTreatmentDatetime;

    @ApiModelProperty(value = "就诊流水号类型：	0-门急诊；	1-住院；", required = true)
    private Integer treatmentSerialNumberType;

    @ApiModelProperty(value = "就诊流水号")
    private String treatmentSerialNumber;

    @ApiModelProperty(value = "票据完整度")
    private String completeStatus;

    @ApiModelProperty(value = "保险类型")
    private Integer type;

    @ApiModelProperty(value = "险种")
    private List<Integer> types;

    @ApiModelProperty(value = "保险公司id")
    private Long companyId;

    @ApiModelProperty(value = "状态列表")
    private List<Integer> statusList;
}
