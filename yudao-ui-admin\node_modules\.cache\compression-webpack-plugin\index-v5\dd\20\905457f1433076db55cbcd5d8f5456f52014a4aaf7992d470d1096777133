
b4f2286cc320638b8d69cc5846899b13fa05c7ba	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.48a06580c74766ec91e9.hot-update.js\",\"contentHash\":\"e5dbc4f2025246318e85204108e5daab\"}","integrity":"sha512-hrUbpicNJrbxbR2u6oMfVqw8lT6VG6SU1302ivaohKWcA1GQnCCyMPZjgqotLP0YeoPOSegoRLCeoshSufzB2A==","time":1754360022274,"size":92650}