package cn.iocoder.yudao.framework.irs.core;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.*;
import java.net.URI;
import java.net.URL;
import java.security.Key;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

@Slf4j
public class PdfSigning {
    //天印服务器接口信息
    private static String ProjectID="330000134";
    private static String ProjectSecret="764c59c5cea14e5fb72ad55a19050e8d";
    //    private static String ProjectID="*********";
//    private static String ProjectSecret="47701040b62d46a2864b25dfb842bc6a";
    private static String accessKey="BCDSGA_6dcbfa17ab5ac62a9b7ec718d2393855";
    private static String secretKey="BCDSGS_a009ac6dd5d98ea45ae2a60e31a2c593";
    private static String apiUrl="https://ibcdsg.zj.gov.cn:8443/restapi/prod/**********************/seal-platform/seal/v1/rest/sign/signPdf";

    /**
     * pdf文件盖章
     * 接口地址：/V1/accounts/outerAccounts/create
     * @return
     */
    public static byte[] createSignPdf(File unsignedPdfFile){
        try{
            JSONObject ReqData = new JSONObject();
            String fileByte1= PdfSigning.PDFToBase64(unsignedPdfFile);
            ReqData.put("fileBase64", fileByte1);
            ReqData.put("sealSn", "33012507260311272676");
            ReqData.put("posX", "80");
            ReqData.put("posY", "0");
            ReqData.put("signType", "4");
            ReqData.put("key", "盖章位置");
            ReqData.put("posPage", "1");
            ReqData.put("fileName", "01.pdf");

            String resp = post(ReqData,"post");
            log.info("电子签章返回结果:"+resp);
            JSONObject jsonObject= JSON.parseObject(resp);
            Boolean success = jsonObject.getBoolean("success");
            if(!success){
                log.error("签章失败:{}", jsonObject.getString("message"));
                throw new ServiceException(500, "签章失败");
            }
            String data = jsonObject.getString("data");
            JSONObject jsondata= JSON.parseObject(data);
            String signFileB64 = jsondata.getString("signFileB64");
            BASE64Decoder decoder = new BASE64Decoder();
            return decoder.decodeBuffer(signFileB64);
        }catch(Exception e){
            throw new RuntimeException(e);
        }
    }



    public static String post( JSONObject data,String requestMethod) throws Exception {

        //计算irs请求头里面参数信息
        DateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = dateFormat.format(new Date());
        URL url = new URL(apiUrl);
        URI uri = new URI(url.getProtocol(), url.getHost(), url.getPath(), url.getQuery(), null);
        String canonicalQueryString = "";
        String message = requestMethod.toUpperCase() + "\n" + uri.getPath() + "\n" + canonicalQueryString + "\n"  + accessKey + "\n" + date + "\n";
        Mac hasher = Mac.getInstance("HmacSHA256");
        hasher.init(new SecretKeySpec(secretKey.getBytes(), "HmacSHA256"));
        byte[] hash = hasher.doFinal(message.getBytes());
        DatatypeConverter.printHexBinary(hash);
        String sign = DatatypeConverter.printBase64Binary(hash);
            /*byte[] stream1 = message.toString().getBytes("UTF-8");
            String sign = sign1(stream1);*/

        // 计算电子印章组件signature值
        byte[] stream = data.toString().getBytes("UTF-8");
        // 签名数据,根据签名算法,对请求数据进行签名
        String signature = sign(stream);
        //System.out.println(signature);
        // 设置HTTP请求头
        HttpEntityEnclosingRequestBase req = new HttpPost(apiUrl);
        // project-id为用户的projectId
        req.addHeader("appId", ProjectID);
        // signature为之前生成的签名
        req.addHeader("signature", signature);
        req.addHeader("X-BG-HMAC-SIGNATURE", sign);
        req.addHeader("X-BG-HMAC-ALGORITHM", "hmac-sha256");
        req.addHeader("X-BG-HMAC-ACCESS-KEY", accessKey);
        req.addHeader("X-BG-DATE-TIME", date);
        req.addHeader("Content-Type", "application/json");

        // 设置HTTP请求体
        HttpEntity entity = new ByteArrayEntity(stream, ContentType
                .create(ContentType.APPLICATION_JSON.getMimeType(), "UTF-8"));
        req.setEntity(entity);

        // 执行请求
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient cli = httpClientBuilder.build();
        HttpResponse res = cli.execute(req);
        int statusCode = res.getStatusLine().getStatusCode();
        System.out.println(statusCode);
        if(200 != statusCode){
            System.out.println(statusCode);
        }
        // 获取响应
        InputStream in = res.getEntity().getContent();

        byte[] resp = readStream(in);
        String strRes = new String(resp,"UTF-8");
        System.out.println(strRes);
        cli.close();
        return strRes;
    }

    private static String sign(byte[] stream)
            throws Exception {
        // 获取消息验证码类的实例，算法选择"HmacSHA256"
        Mac mac = Mac.getInstance("HmacSHA256");

        // 获取安全密钥
        Key secKey = new SecretKeySpec(
                ProjectSecret.getBytes("UTF-8"),
                mac.getAlgorithm());

        // 初始化
        mac.init(secKey);

        // 获得签名
        byte[] sign = mac.doFinal(stream);

        // 将byte[]格式的签名用binary编码转化为字符串返回
        return binaryEncode(sign);

    }
    private static String sign1(byte[] stream)
            throws Exception {
        // 获取消息验证码类的实例，算法选择"HmacSHA256"
        Mac mac = Mac.getInstance("HmacSHA256");

        // 获取安全密钥
        Key secKey = new SecretKeySpec(
                secretKey.getBytes("UTF-8"),
                mac.getAlgorithm());

        // 初始化
        mac.init(secKey);

        // 获得签名
        byte[] sign = mac.doFinal(stream);

        // 将byte[]格式的签名用binary编码转化为字符串返回
        return binaryEncode(sign);

    }

    public static String binaryEncode(byte[] data) {
        final char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8',
                '9', 'a', 'b', 'c', 'd', 'e', 'f' };

        StringBuilder builder = new StringBuilder();

        for (byte i : data) {
            builder.append(hexDigits[i >>> 4 & 0xf]);
            builder.append(hexDigits[i & 0xf]);
        }

        return builder.toString();
    }

    public static byte[] readStream(InputStream in) throws IOException {

        ByteArrayOutputStream output = new ByteArrayOutputStream();

        byte[] buffer = new byte[1024 * 10];
        try {

            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                output.write(buffer, 0, n);
            }

            return output.toByteArray();

        } finally {
            in.close();
            output.close();
        }
    }

    /**
     * Description: 将pdf文件转换为Base64编码
     * @param  要转的的pdf文件
     * <AUTHOR>
     * Create Date: 2015年8月3日 下午9:52:30
     */
    public static String PDFToBase64(File file) {
        BASE64Encoder encoder = new BASE64Encoder();
        FileInputStream fin =null;
        BufferedInputStream bin =null;
        ByteArrayOutputStream baos = null;
        BufferedOutputStream bout =null;
        try {
            fin = new FileInputStream(file);
            bin = new BufferedInputStream(fin);
            baos = new ByteArrayOutputStream();
            bout = new BufferedOutputStream(baos);
            byte[] buffer = new byte[1024];
            int len = bin.read(buffer);
            while(len != -1){
                bout.write(buffer, 0, len);
                len = bin.read(buffer);
            }
            //刷新此输出流并强制写出所有缓冲的输出字节
            bout.flush();
            byte[] bytes = baos.toByteArray();
            return Base64.encode(bytes);
            //return encoder.encodeBuffer(bytes);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            try {
                fin.close();
                bin.close();
                bout.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * Description: 将base64编码内容转换为Pdf
     * @param  base64编码内容，文件的存储路径（含文件名）
     * <AUTHOR>
     * Create Date: 2015年7月30日 上午9:40:23
     */
    public static void base64StringToPdf(String base64Content,String filePath){
        BASE64Decoder decoder = new BASE64Decoder();
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;

        try {
            byte[] bytes = decoder.decodeBuffer(base64Content);//base64编码内容转换为字节数组
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(bytes);
            bis = new BufferedInputStream(byteInputStream);
            File file = new File(filePath);
            File path = file.getParentFile();
            if(!path.exists()){
                path.mkdirs();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);

            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while(length != -1){
                bos.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            bos.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            //closeStream(bis, fos, bos);
        }
    }
}
