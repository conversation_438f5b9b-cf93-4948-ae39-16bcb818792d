
97399bd932de216d9c9932362eb19295e84b2f3f	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.aec0464d688862c34d5f.hot-update.js\",\"contentHash\":\"288ee6438d804f45180c9cd07f3fc2c7\"}","integrity":"sha512-g4K59/CKDoi1g0h2tpqsN1zNxxKs1ihd+3KCUTwjH8Ip7P7jsQh5MMNmebU6Ly2jkavC7SVJ2vpksHAIZ+P3Pg==","time":1754360974396,"size":92995}