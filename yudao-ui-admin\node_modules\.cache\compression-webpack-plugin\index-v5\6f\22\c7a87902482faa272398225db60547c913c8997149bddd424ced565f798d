
4f87215ad6a0d614251ab4a6396fab90f0fa1587	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"ee527e81abfa41a17b8e485f1868d347\"}","integrity":"sha512-sQLtEskLp041OnuG9HDbSBI2eLILvLiAGHFWFbVAM0aws3IR6XSDh//AW2KyCNtA1T3yM5wt3ASj1BcTv2fXCQ==","time":1754360443412,"size":3474915}