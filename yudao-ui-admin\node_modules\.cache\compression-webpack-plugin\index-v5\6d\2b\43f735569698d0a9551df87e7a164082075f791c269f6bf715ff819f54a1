
2056f316fda711f8c856948d60d4ea4734a4757a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.d5c2c33818739275c600.hot-update.js\",\"contentHash\":\"9e02095f90a46d86648514c0bcc90278\"}","integrity":"sha512-tIlx20euR/EEnuB+H627RFKpM0viAz6mejSb/aJEuRSBafcdqcGS222Qi9b747Guk7WL+WzS1IBrNhsSaDuknA==","time":1754358526117,"size":85323}