
516a0b0f0c433a7fc70910b783a71ceb06dff52a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"2659719e3b0ce2fca33e59a198d3654b\"}","integrity":"sha512-aR6tD9/NM/D/JKSjj4WSKkxGO1g+uhHRjUWC7QGIZuMXGdQ7LG1uDxkC9q0k9GafFxJ6ClDEqevUcU3p3HVuGw==","time":1754360489302,"size":16761990}