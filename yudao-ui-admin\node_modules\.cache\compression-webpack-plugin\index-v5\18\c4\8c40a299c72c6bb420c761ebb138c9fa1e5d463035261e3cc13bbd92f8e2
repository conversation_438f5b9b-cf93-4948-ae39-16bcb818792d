
9219dc7b83d713f39d0dc2dfc62f9968c8f45bf0	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"9f4ef3275c3af26422e48c0eb34d006e\"}","integrity":"sha512-RHoPfPqz6ig/+ZQz+GhYBq/Y+L32cuWpIuVac2v8vItJii0udoy7UBkV80VDL3I/ctQAUrsCoD1OuCcGyzYpLw==","time":1754356172679,"size":16759167}