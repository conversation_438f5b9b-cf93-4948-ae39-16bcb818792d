
7ca07e4518f4d2b33ae635855963c7113e7df55a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"1dd1ae90b9a0b7bf7d06491f747e0743\"}","integrity":"sha512-xc18YxWgu6BAm2TNBGD3QQExUBLToxm+PVTmsjeW8XXfQHu0E1jtVhasohRyC7sGR9BVEUSvYbWlhIDQxMXTQw==","time":1754357787193,"size":3474284}