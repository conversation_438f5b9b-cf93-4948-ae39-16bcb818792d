
01247c71bfe05703896fc3f3e1edc8fcc506ce06	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"dbfecce2c5b7892431eab55f642d8a9a\"}","integrity":"sha512-pYikcTL47H0eAYAyBogJlAZAvrla+HmZXhdlNKKNiVaW8ktz2VdxUjn5y5XIRG1uJgAN4dM1DEUhV80d8DbFfA==","time":1754358296232,"size":3473691}