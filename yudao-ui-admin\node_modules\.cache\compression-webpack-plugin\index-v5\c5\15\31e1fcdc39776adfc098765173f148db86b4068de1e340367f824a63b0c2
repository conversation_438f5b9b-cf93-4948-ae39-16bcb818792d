
1de9a006a1766a1a2950f1085aa64c02a062f304	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"3b72b47eba1671de57f3cfc9130b91d8\"}","integrity":"sha512-UGpyhTBqCeDH1RNrlnkUa2qACKuTuja3QvHU2JsSY45IeLAxBofq5T5R8VMEFABfXFWa28qyHKpO13R4oqobTg==","time":1754360023481,"size":16759949}