
7895096238f816abc1c136be47d7e1ddfaa9c55c	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"562e5d517c8649b16abbf82ce04361ef\"}","integrity":"sha512-GaG7BNPBee42AwNL1W4J543S7q9VS0My4stHznYWX91EKeTbC+rW62PREyp9fwC+tjCF2+EBAODTKH7ZC/mcvw==","time":1754358309071,"size":3474913}