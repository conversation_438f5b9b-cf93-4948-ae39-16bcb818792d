
930273e7b0a46897afd385dc810acb76cc14d83a	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.7d05e0b07793f602e92b.hot-update.js\",\"contentHash\":\"4c215262488fe89f3f06ec31197befa6\"}","integrity":"sha512-Xu6Unf11Wf3kjp7Vtv/tGtvwRPml2WM6aoAq4SaZRMR+n1mjajHjZtsOpt+c7/mXQU/AT0w1xKHZ5zQG+UFxxw==","time":1754360488159,"size":93065}