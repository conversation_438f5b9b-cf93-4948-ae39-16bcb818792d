
1666584cdd816df2f7f38a9fab93ecd7bde3662e	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"afaef6590eff2a16cea2291ad3c6cab3\"}","integrity":"sha512-cyK2oBSKFXRRthUEwBHWE2aKwx81sCq9PkvnrL7tFbI8GSJMLzx908umP6sRuc5ix6bN96cZaYamfekf1Fmelw==","time":1754360444289,"size":16762857}