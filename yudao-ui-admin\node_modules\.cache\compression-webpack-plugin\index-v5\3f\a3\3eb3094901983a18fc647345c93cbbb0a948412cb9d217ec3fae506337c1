
089b3d4d6e03e7f58223658c04fcddc396037be6	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"21e25766046808b973354f0a3efd9c5b\"}","integrity":"sha512-LIu3pfVTpT56GLDMBYBlsv62h2tBj9CvdhB8Gd5LbnYARWbtu92TMDpzEOwXWEjYy3PirxpFCmrRpa2IsH67iA==","time":1754360457380,"size":16761605}