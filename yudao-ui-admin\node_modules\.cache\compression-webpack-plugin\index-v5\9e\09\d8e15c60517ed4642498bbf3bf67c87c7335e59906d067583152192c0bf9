
5b82d67eb708794aa9457c8a1e457b668c61d548	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F18.js\",\"contentHash\":\"a83f410056b4f68ce302293ee5691ead\"}","integrity":"sha512-9X9Gc19VEEkxsaQx7DkzWePgOI8oxnCmX5nug2HxWGwLRAXLmm4cmX7ljGa1LTUpXnwcSG+ZWHzALHOTLfkrdA==","time":1754358556708,"size":16759875}