
36e10839d8fe38423c567a9dd237b1b7419ff5b1	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"01f831ee195a48494bdffe7a4eb4da35\"}","integrity":"sha512-MEvav2WSMEa44M+hAG/QDgRwNUZbka0k4kyEb8go/m1wzEb38NP2hHZYpxLavXBC+ydqQ/sU5V1CeP9HAiuBhw==","time":1754358497469,"size":3473689}