{"remainingRequest": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\area\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\src\\views\\insurance\\area\\index.vue", "mtime": 1754358495639}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\babel.config.js", "mtime": 1691889630125}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\babel-loader@8.2.5_@babel+core@7.18.6_webpack@4.46.0\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1667694382043}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\cache-loader@4.1.0_webpack@4.46.0\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1667694382039}, {"path": "C:\\projects\\shenlan\\insurance\\yudao-ui-admin\\node_modules\\.pnpm\\vue-loader@15.9.8_@vue+compiler-sfc@3.2.37_cache-loader@4.1.0_css-loader@3.6.0_vue-template-c_al5g3iuig4vajxdzqv2qwlkix4\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1667694382624}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";;;;;;;;;;;;;AAkGA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA,YADA;EAEAC;IACAC,qCADA;IAEAC,yCAFA;IAGAC;EAHA,CAFA;EAOAC,IAPA,kBAOA;IACA;MACA;MACAC,aAFA;MAGA;MACAC,oBAJA;MAKA;MACAC,gBANA;MAOA;MACAC,QARA;MASA;MACAC,QAVA;MAWA;MACAC,SAZA;MAaA;MACAC,WAdA;MAeA;MACAC;QACAC,SADA;QAEAC,YAFA;QAGAf,UAHA;QAIAgB;MAJA,CAhBA;MAsBA;MACAC,QAvBA;MAwBA;MACAC;QACAlB;UAAAmB;UAAAC;UAAAC;QAAA;MADA,CAzBA;MA4BAC;QACAC,eADA;QAEAC,iBAFA;QAGAC,iBAHA;QAIAC;MAJA;IA5BA;EAmCA,CA3CA;EA4CAC,OA5CA,qBA4CA;IACA;EACA,CA9CA;EA+CAC;IACA;IACAC,OAFA,qBAEA;MAAA;;MACA,oBADA,CAEA;;MACA,+DAHA,CAIA;;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CAZA;;IAaA;IACAC,MAdA,oBAcA;MACA;MACA;IACA,CAjBA;;IAkBA;IACAC,KAnBA,mBAmBA;MACA;QACAC,aADA;QAEAhC,eAFA;QAGAgB,oBAHA;QAIAiB;MAJA;MAMA;IACA,CA3BA;;IA4BA;IACAC,WA7BA,yBA6BA;MACA;MACA;IACA,CAhCA;;IAiCA;IACAC,UAlCA,wBAkCA;MACA;MACA;IACA,CArCA;;IAsCA;IACAC,SAvCA,uBAuCA;MACA;MACA;MACA;IACA,CA3CA;;IA4CA;IACAC,YA7CA,wBA6CAC,GA7CA,EA6CA;MAAA;;MACA;MACA;MACA;QACA;QACA;QACA;MACA,CAJA;IAKA,CArDA;;IAsDA;IACAC,UAvDA,wBAuDA;MAAA;;MACA;QACA;UACA;QACA,CAHA,CAIA;;;QACA;UACA;YACA;;YACA;;YACA;UACA,CAJA;UAKA;QACA,CAZA,CAaA;;;QACA;UACA;;UACA;;UACA;QACA,CAJA;MAKA,CAnBA;IAoBA,CA5EA;;IA6EA;IACAC,YA9EA,wBA8EAF,GA9EA,EA8EA;MAAA;;MACA;MACA;QACA;MACA,CAFA,EAEAG,IAFA,CAEA;QACA;;QACA;MACA,CALA,EAKAC,KALA,CAKA,cALA;IAMA,CAtFA;;IAuFA;IACAC,YAxFA,0BAwFA;MAAA;;MACA;MACA;MACAC;MACAA,4BAJA,CAKA;;MACA;QACA;QACA;MACA,CAHA,EAGAH,IAHA,CAGA;QACA;;QACA;MACA,CANA,EAMAC,KANA,CAMA,cANA;IAOA,CArGA;IAsGAG,iBAtGA,6BAsGAP,GAtGA,EAsGA;MACA;MACA;IACA,CAzGA;IA0GAQ,gBA1GA,4BA0GAR,GA1GA,EA0GA;MAAA;;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA,CA/GA;IAgHAS,qBAhHA,mCAgHA;MACA;IACA,CAlHA;IAmHAC,qBAnHA,mCAmHA;MACA;MACA;IACA,CAtHA;IAuHAC,8BAvHA,4CAuHA;MACA;MACA;IACA;EA1HA;AA/CA,C", "names": ["name", "components", "CompanySelect", "AreaCompanyList", "AreaCompanyChoose", "data", "loading", "exportLoading", "showSearch", "total", "list", "title", "open", "queryParams", "pageNo", "pageSize", "companyId", "form", "rules", "required", "message", "trigger", "areaCompany", "listOpen", "createOpen", "areaId", "chooseTitle", "created", "methods", "getList", "cancel", "reset", "id", "subArea", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleUpdate", "row", "submitForm", "handleDelete", "then", "catch", "handleExport", "params", "handleViewCompany", "handleSetCompany", "submitAreaCompanyForm", "cancelAreaCompanyForm", "handleAreaCompanySubmitSuccess"], "sourceRoot": "src/views/insurance/area", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n\r\n    <!-- 搜索工作栏 -->\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"区域名称\" prop=\"name\">\r\n        <el-input v-model=\"queryParams.name\" placeholder=\"请输入区域名称\" clearable @keyup.enter.native=\"handleQuery\"/>\r\n      </el-form-item>\r\n      <el-form-item label=\"保险公司\" prop=\"companyId\">\r\n        <CompanySelect v-model=\"queryParams.companyId\"/>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 操作工具栏 -->\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\"\r\n                   v-hasPermi=\"['insurance:area:create']\">新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" :loading=\"exportLoading\"\r\n                   v-hasPermi=\"['insurance:area:export']\">导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <!-- 列表 -->\r\n    <el-table v-loading=\"loading\" :data=\"list\">\r\n      <el-table-column label=\"ID\" align=\"center\" prop=\"id\" />\r\n      <el-table-column label=\"区域名称\" align=\"center\" prop=\"name\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"更新时间\" align=\"center\" prop=\"updateTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updateTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleViewCompany(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:query']\">查看负责公司</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleSetCompany(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:update']\">关联负责公司</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:update']\">修改</el-button>\r\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"\r\n                     v-hasPermi=\"['insurance:area:delete']\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <!-- 分页组件 -->\r\n    <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNo\" :limit.sync=\"queryParams.pageSize\"\r\n                @pagination=\"getList\"/>\r\n\r\n    <!-- 对话框(添加 / 修改) -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"区域名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入区域名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"子区域\" prop=\"subArea\">\r\n          <el-select\r\n            v-model=\"form.subArea\"\r\n            multiple\r\n            filterable\r\n            allow-create\r\n            default-first-option\r\n            placeholder=\"请选择子区域\">\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"负责公司列表\" :visible.sync=\"areaCompany.listOpen\" width=\"500px\" append-to-body>\r\n      <AreaCompanyList v-if=\"areaCompany.listOpen\" :areaId=\"areaCompany.areaId\" />\r\n    </el-dialog>\r\n    <el-dialog :title=\"areaCompany.chooseTitle\" :visible.sync=\"areaCompany.createOpen\" width=\"500px\" append-to-body>\r\n      <AreaCompanyChoose ref=\"companyChooser\" :areaId=\"areaCompany.areaId\" @submitSuccess=\"handleAreaCompanySubmitSuccess\"/>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitAreaCompanyForm\">确 定</el-button>\r\n        <el-button @click=\"cancelAreaCompanyForm\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { createArea, updateArea, deleteArea, getArea, getAreaPage, exportAreaExcel } from \"@/api/insurance/area\";\r\nimport CompanySelect from \"@/views/insurance/company/components/companySelect\";\r\nimport AreaCompanyList from \"./areaCompanyList\";\r\nimport AreaCompanyChoose from \"./areaCompanyChoose\";\r\nexport default {\r\n  name: \"Area\",\r\n  components: {\r\n    CompanySelect,\r\n    AreaCompanyList,\r\n    AreaCompanyChoose\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 导出遮罩层\r\n      exportLoading: false,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 区域列表\r\n      list: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNo: 1,\r\n        pageSize: 10,\r\n        name: null,\r\n        companyId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [{ required: true, message: \"区域名称不能为空\", trigger: \"blur\" }],\r\n      },\r\n      areaCompany: {\r\n        listOpen: false,\r\n        createOpen: false,\r\n        areaId: undefined,\r\n        chooseTitle: undefined,\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      // 执行查询\r\n      getAreaPage(params).then(response => {\r\n        this.list = response.data.list;\r\n        this.total = response.data.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 取消按钮 */\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    /** 表单重置 */\r\n    reset() {\r\n      this.form = {\r\n        id: undefined,\r\n        name: undefined,\r\n        companyId: undefined,\r\n        subArea: [],\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNo = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加区域\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id;\r\n      getArea(id).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改区域\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (!valid) {\r\n          return;\r\n        }\r\n        // 修改的提交\r\n        if (this.form.id != null) {\r\n          updateArea(this.form).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.open = false;\r\n            this.getList();\r\n          });\r\n          return;\r\n        }\r\n        // 添加的提交\r\n        createArea(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\");\r\n          this.open = false;\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const id = row.id;\r\n      this.$modal.confirm('是否确认删除区域编号为\"' + id + '\"的数据项?').then(function() {\r\n          return deleteArea(id);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 处理查询参数\r\n      let params = {...this.queryParams};\r\n      params.pageNo = undefined;\r\n      params.pageSize = undefined;\r\n      // 执行导出\r\n      this.$modal.confirm('是否确认导出所有区域数据项?').then(() => {\r\n          this.exportLoading = true;\r\n          return exportAreaExcel(params);\r\n        }).then(response => {\r\n          this.$download.excel(response, '区域.xls');\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    handleViewCompany(row) {\r\n      this.areaCompany.listOpen = true;\r\n      this.areaCompany.areaId = row.id;\r\n    },\r\n    handleSetCompany(row) {\r\n      this.areaCompany.createOpen = true;\r\n      this.areaCompany.areaId = row.id;\r\n      this.areaCompany.chooseTitle = `关联负责公司:${row.name}`;\r\n      this.$nextTick(()=>this.$refs.companyChooser.reset()) \r\n    },\r\n    submitAreaCompanyForm() {\r\n      this.$refs.companyChooser.submit();\r\n    },\r\n    cancelAreaCompanyForm() {\r\n      this.areaCompany.createOpen = false;\r\n      this.$refs.companyChooser.reset()\r\n    },\r\n    handleAreaCompanySubmitSuccess() {\r\n      this.$modal.msgSuccess(\"关联负责公司成功\");\r\n      this.areaCompany.createOpen = false;\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}