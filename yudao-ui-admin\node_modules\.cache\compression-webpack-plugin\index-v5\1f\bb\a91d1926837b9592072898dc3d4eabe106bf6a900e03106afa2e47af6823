
a87b4d578a5fa2c5a085264ba7ba22a9c5b2ef81	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.f50d61fd35b0be88bbef.hot-update.js\",\"contentHash\":\"483d7b29a2bd49e6db61b59e837a73c1\"}","integrity":"sha512-UwvSDeOjhcsgpghzaXO9K8ySMxx/U6hcaOtlBYLD3wxGAhlZdXtLnaROU1VfDEZA5Xnhb3nwbG91xD8cR5l9ow==","time":1754358308673,"size":85965}