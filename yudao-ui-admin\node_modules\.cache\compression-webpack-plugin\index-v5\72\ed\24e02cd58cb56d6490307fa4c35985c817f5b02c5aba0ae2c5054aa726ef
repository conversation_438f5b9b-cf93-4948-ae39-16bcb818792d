
c53a926bcc5054a727ad16b38633a49ec2a045b8	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"18.b20929cfa5e8634048b8.hot-update.js\",\"contentHash\":\"4d6909843382f2adac70f2c11dc75313\"}","integrity":"sha512-LIGwsWdAwnueaIixCo2bV/bN0rRmF2l4/RvVqkIye+A2Lsh5I4H66JuT/lSQnBqxOWqQUKHQSzsX8KfqT9jB/w==","time":1754360472446,"size":93066}