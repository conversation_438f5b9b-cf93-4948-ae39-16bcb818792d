
4654d8ff9df6edbc55a2a924f9d8615a68376daf	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"0060fb32302a798545a9697f39cd9990\"}","integrity":"sha512-T0HAE8oNR4pvjW0UxYDjPQy203gAIJqsj6cqfjN8T2GnZfUakxF69GTPOHD9FKEmRGEcSWvGsnPkd2RniSMV/g==","time":1754360488464,"size":3474350}