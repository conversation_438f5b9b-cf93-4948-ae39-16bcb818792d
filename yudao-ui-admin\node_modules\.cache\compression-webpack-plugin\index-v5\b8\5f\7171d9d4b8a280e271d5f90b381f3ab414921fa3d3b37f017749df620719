
49dd2471b5c6f586b28689991db850acf3a71e9f	{"key":"{\"nodeVersion\":\"v14.21.1\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"fefa0fc6b720aaad7f791374f74c9a8d\"}","integrity":"sha512-QbJhJdgvc+ZB8igWFYpb5h7YTjR4vWTgaDtN2NUBhUSDbIsMwmQkhJQBIgvEuMSdb1dIKFh4rPdnic/DJ/DY9Q==","time":1754359339560,"size":3474350}